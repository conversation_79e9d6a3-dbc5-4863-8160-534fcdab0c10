<?php

declare(strict_types=1);

namespace App\Http\Requests\Clinics;

use Illuminate\Foundation\Http\FormRequest;

final class PreviouslyPurchasedIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $clinic = $this->route('clinic');

        if ($clinic) {
            return $this->user()->can('view', $clinic);
        }

        $clinicId = $this->header('Highfive-Clinic');

        if ($clinicId) {
            $clinic = \App\Models\Clinic::find($clinicId);
            return $clinic && $this->user()->can('view', $clinic);
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'filter.search' => ['nullable', 'string', 'max:255'],
            'sort' => ['nullable', 'string', 'in:last_ordered,-last_ordered'],
            'page' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }
}
